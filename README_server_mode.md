# LiveCaptions Server模式使用说明

## 概述

C#程序现在支持server模式，可以通过管道与Node.js进行高效通信。主要改进：

1. **Server模式**: 程序启动后保持运行，通过stdin/stdout进行JSON通信
2. **多线程watch**: `get_caption --watch` 现在使用独立线程，不会阻塞其他命令
3. **更快的响应**: 避免了每次命令都要启动新进程的开销

## C#程序改动

### 新增命令

- `server`: 启动server模式，通过管道进行通信
- `start_watch`: 在server模式下启动字幕监听（多线程）
- `stop_watch`: 停止字幕监听

### Server模式通信协议

**输入格式** (发送到stdin):
```json
{"command": "get_caption", "args": []}
{"command": "set_language", "args": ["--lang", "Chinese"]}
{"command": "start_watch", "args": []}
```

**输出格式** (从stdout接收):
```json
{"caption": "字幕内容"}
{"success": true, "message": "watch已启动"}
{"type": "caption_update", "caption": "实时字幕更新"}
```

## Node.js客户端使用

### 1. 基本使用

```javascript
const LiveCaptionsClient = require('./nodejs_client_example');

async function example() {
    const client = new LiveCaptionsClient('./LiveCaptionsTool.exe');
    
    // 连接到server
    await client.connect();
    
    // 执行命令
    const status = await client.getStatus();
    const caption = await client.getCaption();
    
    // 断开连接
    client.disconnect();
}
```

### 2. 监听字幕变化（多线程）

```javascript
// 设置字幕更新回调
client.on('captionUpdate', (caption) => {
    console.log('字幕更新:', caption);
});

// 启动监听（不会阻塞）
await client.startWatch();

// 可以继续执行其他命令
const languages = await client.listLanguages();

// 停止监听
await client.stopWatch();
```

### 3. 可用的API方法

```javascript
// 窗口控制
await client.getStatus()           // 获取窗口状态
await client.startLiveCaptions()   // 启动LiveCaptions
await client.stopLiveCaptions()    // 停止LiveCaptions
await client.hideWindow()          // 隐藏窗口
await client.showWindow()          // 显示窗口

// 字幕功能
await client.getCaption()          // 获取当前字幕
await client.startWatch()          // 开始监听字幕变化
await client.stopWatch()           // 停止监听

// 语言设置
await client.listLanguages()       // 获取语言列表
await client.setLanguage('Chinese') // 设置语言

// 音频设置
await client.enableMicAudio()      // 启用麦克风音频
await client.disableMicAudio()     // 禁用麦克风音频

// 其他
await client.getReadyText()        // 获取准备文本
```

## 运行示例

### 1. 编译C#程序
```bash
csc c#_live_caption.cs
```

### 2. 运行Node.js示例
```bash
node simple_example.js
```

### 3. 手动测试server模式
```bash
# 启动server模式
./LiveCaptionsTool.exe server

# 然后在stdin输入JSON命令：
{"command": "status", "args": []}
{"command": "get_caption", "args": []}
{"command": "start_watch", "args": []}
```

## 性能对比

### 之前的方式 (spawn每次启动新进程)
- 每个命令需要启动新进程: ~500-1000ms
- 无法并发执行命令
- watch模式会阻塞其他操作

### 现在的方式 (server模式 + 管道通信)
- 命令响应时间: ~10-50ms
- 支持并发命令执行
- watch使用独立线程，不阻塞其他命令

## 注意事项

1. **编码**: 确保C#程序和Node.js都使用UTF-8编码
2. **错误处理**: 客户端包含超时和错误重试机制
3. **资源清理**: 使用完毕后记得调用`client.disconnect()`
4. **多线程安全**: watch线程与主线程之间的通信是安全的

## 故障排除

1. **连接失败**: 检查exe文件路径是否正确
2. **命令超时**: 增加超时时间或检查LiveCaptions是否正常运行
3. **字幕获取失败**: 确保LiveCaptions窗口已启动且可访问
4. **编码问题**: 确保控制台支持UTF-8编码
