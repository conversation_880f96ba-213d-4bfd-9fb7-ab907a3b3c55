@echo off
echo 正在编译LiveCaptions工具...

REM 查找.NET Framework的csc.exe
set CSC_PATH=""

REM 尝试不同的.NET Framework版本路径
if exist "C:\Windows\Microsoft.NET\Framework64\v4.0.30319\csc.exe" (
    set CSC_PATH="C:\Windows\Microsoft.NET\Framework64\v4.0.30319\csc.exe"
) else if exist "C:\Windows\Microsoft.NET\Framework\v4.0.30319\csc.exe" (
    set CSC_PATH="C:\Windows\Microsoft.NET\Framework\v4.0.30319\csc.exe"
) else (
    echo 错误: 找不到C#编译器 (csc.exe)
    echo 请确保已安装.NET Framework 4.0或更高版本
    pause
    exit /b 1
)

echo 使用编译器: %CSC_PATH%

REM 编译程序
%CSC_PATH% /reference:UIAutomationClient.dll /reference:UIAutomationTypes.dll c#_live_caption.cs /out:LiveCaptionsTool.exe

if %ERRORLEVEL% EQU 0 (
    echo ✅ 编译成功! 生成文件: LiveCaptionsTool.exe
    echo.
    echo 使用方法:
    echo   单次命令: LiveCaptionsTool.exe status
    echo   Server模式: LiveCaptionsTool.exe server
    echo   Node.js示例: npm start
) else (
    echo ❌ 编译失败!
    echo 请检查代码是否有语法错误
)

pause
