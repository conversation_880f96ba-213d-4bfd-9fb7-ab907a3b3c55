@echo off
echo 🚀 正在构建依赖框架的 Windows Live Caption 控制工具...
echo 📝 注意: 目标电脑需要安装 .NET 8.0 Runtime
echo.

REM 检查是否安装了 .NET SDK
dotnet --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 错误: 未找到 .NET SDK
    echo 请从以下地址下载并安装 .NET 8.0 SDK:
    echo https://dotnet.microsoft.com/download/dotnet/8.0
    pause
    exit /b 1
)

echo ✅ 检测到 .NET SDK 版本:
dotnet --version
echo.

REM 清理之前的构建
echo 🧹 清理之前的构建文件...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"
if exist "publish_framework" rmdir /s /q "publish_framework"
echo.

REM 构建依赖框架的可执行文件
echo 📦 开始构建依赖框架应用程序...
echo 目标平台: Windows x64
echo 模式: 依赖 .NET 8.0 Runtime
echo.

dotnet publish -c Release -r win-x64 --self-contained false -o publish_framework

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 构建成功!
    echo.
    echo 📁 输出文件位置: publish_framework\win_offline_asr.exe
    echo 📊 文件大小:
    dir publish_framework\win_offline_asr.exe | findstr "win_offline_asr.exe"
    echo.
    echo 🎯 部署说明:
    echo   - 生成的 exe 文件较小 (约 1-5MB)
    echo   - 目标电脑必须安装 .NET 8.0 Runtime
    echo   - Runtime 下载地址: https://dotnet.microsoft.com/download/dotnet/8.0
    echo.
    echo 📋 目标电脑安装 Runtime 的方法:
    echo   1. 访问: https://dotnet.microsoft.com/download/dotnet/8.0
    echo   2. 下载 "ASP.NET Core Runtime 8.0.x" (包含桌面应用支持)
    echo   3. 或者下载 ".NET Desktop Runtime 8.0.x"
    echo   4. 安装后即可运行 win_offline_asr.exe
    echo.
    echo 🧪 测试命令:
    echo   publish_framework\win_offline_asr.exe status
    echo   publish_framework\win_offline_asr.exe server
    
    REM 测试生成的文件
    echo.
    echo 🔍 正在测试生成的可执行文件...
    publish_framework\win_offline_asr.exe --help
    
    if %ERRORLEVEL% EQU 0 (
        echo ✅ 可执行文件测试通过!
    ) else (
        echo ⚠️  可执行文件可能有问题，请检查
    )
    
) else (
    echo.
    echo ❌ 构建失败!
    echo 请检查以下可能的问题:
    echo   1. .NET SDK 版本是否为 8.0 或更高
    echo   2. c#_live_caption.cs 文件是否存在语法错误
    echo   3. 项目文件 win_offline_asr.csproj 是否正确
)

echo.
pause
