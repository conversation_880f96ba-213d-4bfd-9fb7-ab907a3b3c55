@echo off
echo 🚀 正在构建自包含的 Windows Live Caption 控制工具...
echo.

REM 检查是否安装了 .NET SDK
dotnet --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 错误: 未找到 .NET SDK
    echo 请从以下地址下载并安装 .NET 8.0 SDK:
    echo https://dotnet.microsoft.com/download/dotnet/8.0
    pause
    exit /b 1
)

echo ✅ 检测到 .NET SDK 版本:
dotnet --version
echo.

REM 清理之前的构建
echo 🧹 清理之前的构建文件...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"
if exist "publish" rmdir /s /q "publish"
echo.

REM 构建自包含的可执行文件
echo 📦 开始构建自包含应用程序...
echo 目标平台: Windows x64
echo 模式: 自包含单文件
echo.

dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:PublishTrimmed=false -o publish

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 构建成功!
    echo.
    echo 📁 输出文件位置: publish\win_offline_asr.exe
    echo 📊 文件大小:
    dir publish\win_offline_asr.exe | findstr "win_offline_asr.exe"
    echo.
    echo 🎯 部署说明:
    echo   - 生成的 exe 文件包含了完整的 .NET 运行时
    echo   - 可以在没有安装 .NET 的 Windows 电脑上直接运行
    echo   - 文件较大 (约 60-80MB) 但无需额外依赖
    echo.
    echo 🧪 测试命令:
    echo   publish\win_offline_asr.exe status
    echo   publish\win_offline_asr.exe server
    echo.
    echo 📋 使用方法:
    echo   1. 将 publish\win_offline_asr.exe 复制到目标电脑
    echo   2. 直接运行，无需安装 .NET Runtime
    echo   3. 支持所有原有功能和 Server 模式
    
    REM 测试生成的文件
    echo.
    echo 🔍 正在测试生成的可执行文件...
    publish\win_offline_asr.exe --help
    
    if %ERRORLEVEL% EQU 0 (
        echo ✅ 可执行文件测试通过!
    ) else (
        echo ⚠️  可执行文件可能有问题，请检查
    )
    
) else (
    echo.
    echo ❌ 构建失败!
    echo 请检查以下可能的问题:
    echo   1. .NET SDK 版本是否为 8.0 或更高
    echo   2. c#_live_caption.cs 文件是否存在语法错误
    echo   3. 项目文件 win_offline_asr.csproj 是否正确
    echo.
    echo 🔧 故障排除:
    echo   - 运行 'dotnet build' 查看详细错误信息
    echo   - 检查 Windows SDK 是否已安装
)

echo.
pause
