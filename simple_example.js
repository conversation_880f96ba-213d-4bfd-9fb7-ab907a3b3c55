const LiveCaptionsClient = require('./nodejs_client_example');

async function simpleExample() {
    const client = new LiveCaptionsClient('./LiveCaptionsTool.exe');

    try {
        // 1. 连接到server模式
        console.log('正在连接...');
        await client.connect();
        console.log('✅ 已连接到LiveCaptions server');

        // 2. 检查状态
        const status = await client.getStatus();
        console.log('📊 窗口状态:', status.visible ? '可见' : '隐藏');

        // 3. 启动LiveCaptions（如果还没启动）
        console.log('🚀 启动LiveCaptions...');
        const startResult = await client.startLiveCaptions();
        if (startResult.success) {
            console.log('✅ LiveCaptions已启动');
            if (startResult.ready_text) {
                console.log('📝 准备文本:', startResult.ready_text);
            }
        } else {
            console.log('❌ 启动失败');
        }

        // 4. 隐藏窗口
        console.log('👁️ 隐藏窗口...');
        const hideResult = await client.hideWindow();
        console.log(hideResult.success ? '✅ 窗口已隐藏' : '❌ 隐藏失败');

        // 5. 获取一次字幕
        console.log('📖 获取当前字幕...');
        const caption = await client.getCaption();
        console.log('字幕内容:', caption.caption || '(无内容)');

        // 6. 开始监听字幕变化（多线程模式）
        console.log('👂 开始监听字幕变化...');
        
        // 设置字幕更新回调
        client.on('captionUpdate', (captionText) => {
            const timestamp = new Date().toLocaleTimeString();
            console.log(`[${timestamp}] 🎯 字幕更新: ${captionText || '(空)'}`);
        });

        // 启动监听
        const watchResult = await client.startWatch();
        if (watchResult.success) {
            console.log('✅ 字幕监听已启动（多线程模式）');
            console.log('💡 现在可以继续执行其他命令...');
        }

        // 7. 在监听的同时执行其他命令
        console.log('⏰ 等待5秒，同时监听字幕...');
        await sleep(5000);

        // 8. 在监听期间获取语言列表（证明多线程工作正常）
        console.log('🌍 获取语言列表...');
        const languages = await client.listLanguages(true, true); // 快速模式，不翻译
        console.log(`📋 找到 ${languages.languages.length} 种语言`);
        if (languages.languages.length > 0) {
            console.log('前5种语言:', languages.languages.slice(0, 5));
        }

        // 9. 再等待5秒
        console.log('⏰ 再等待5秒...');
        await sleep(5000);

        // 10. 停止监听
        console.log('🛑 停止字幕监听...');
        const stopWatchResult = await client.stopWatch();
        console.log(stopWatchResult.success ? '✅ 监听已停止' : '❌ 停止失败');

        // 11. 最后再获取一次字幕
        console.log('📖 最后获取一次字幕...');
        const finalCaption = await client.getCaption();
        console.log('最终字幕:', finalCaption.caption || '(无内容)');

        console.log('🎉 示例完成！');

    } catch (error) {
        console.error('❌ 错误:', error.message);
    } finally {
        // 断开连接
        console.log('🔌 断开连接...');
        client.disconnect();
    }
}

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// 运行示例
if (require.main === module) {
    console.log('🎬 LiveCaptions Node.js 客户端示例');
    console.log('=====================================');
    simpleExample();
}

module.exports = simpleExample;
