<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <AssemblyName>win_offline_asr</AssemblyName>
    <RootNamespace>WinOfflineAsr</RootNamespace>
    <UseWindowsForms>true</UseWindowsForms>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    
    <!-- 自包含部署配置 -->
    <SelfContained>true</SelfContained>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <PublishSingleFile>true</PublishSingleFile>
    <PublishTrimmed>false</PublishTrimmed>
    <IncludeNativeLibrariesForSelfExtract>true</IncludeNativeLibrariesForSelfExtract>
    
    <!-- 版本信息 -->
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Version>1.0.0</Version>
    <Product>Windows Live Caption Controller</Product>
    <Description>Windows Live Caption 控制工具 - Server模式支持</Description>
    <Copyright>Copyright © 2024</Copyright>
  </PropertyGroup>

  <ItemGroup>
    <!-- 编译现有的 C# 文件 -->
    <Compile Include="c#_live_caption.cs" />
  </ItemGroup>

  <ItemGroup>
    <!-- Windows Forms 和 UI Automation 引用 -->
    <PackageReference Include="Microsoft.WindowsDesktop.App" Version="8.0.0" />
  </ItemGroup>

</Project>
